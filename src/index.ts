#!/usr/bin/env node

import { XcodeServer } from "./server.js";
import * as os from "os";
import * as path from "path";

/**
 * Enhanced CLI interface with professional status reporting
 */
class XcodeMCPCLI {
  private server: XcodeServer | null = null;
  private startTime: number = 0;

  /**
   * Display startup banner
   */
  private displayBanner(): void {
    const banner = `
╔══════════════════════════════════════════════════════════════╗
║                    XCODE MCP SERVER                          ║
║              Model Context Protocol Server                   ║
║                   for Xcode Integration                      ║
╚══════════════════════════════════════════════════════════════╝

🚀 Starting Xcode MCP Server v1.0.3
📅 ${new Date().toLocaleString()}
💻 Platform: ${os.platform()} ${os.arch()}
🏠 Working Directory: ${process.cwd()}
`;
    console.error(banner);
  }

  /**
   * Display initialization progress
   */
  private displayInitProgress(
    step: string,
    current: number,
    total: number
  ): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar =
      "█".repeat(Math.floor(percentage / 5)) +
      "░".repeat(20 - Math.floor(percentage / 5));
    console.error(`[${progressBar}] ${percentage}% - ${step}`);
  }

  /**
   * Display server status
   */
  private displayServerStatus(server: XcodeServer): void {
    const uptime = Date.now() - this.startTime;
    const activeProject = server.activeProject;

    console.error(`
📊 SERVER STATUS:
─────────────────────────────────────────────────────────────
✅ Status: Running
⏱️  Uptime: ${this.formatUptime(uptime)}
🔧 Tools: 60+ MCP tools registered
🏗️  Architecture: Enterprise-grade with dependency injection
📈 Performance: Advanced caching and monitoring enabled
🔒 Security: Path validation and command injection prevention active

📁 PROJECT CONTEXT:
─────────────────────────────────────────────────────────────`);

    if (activeProject) {
      console.error(`✅ Active Project: ${activeProject.name}`);
      console.error(`📂 Path: ${activeProject.path}`);
      console.error(
        `🏷️  Type: ${activeProject.isWorkspace ? "Workspace" : "Project"}`
      );
      if (activeProject.associatedProjectPath) {
        console.error(
          `🔗 Associated Project: ${activeProject.associatedProjectPath}`
        );
      }
    } else {
      console.error(`⚠️  No active project detected`);
      console.error(`💡 Use set_project_path tool to set an active project`);
    }

    console.error(`
🌐 MCP PROTOCOL:
─────────────────────────────────────────────────────────────
📡 Transport: stdio
🔌 Protocol Version: 2024-11-05
📋 Capabilities: tools, resources
🎯 Ready for AI agent connections

💡 USAGE:
─────────────────────────────────────────────────────────────
Connect your AI agent to this server using the MCP protocol.
All 60+ tools are available for Xcode project management,
file operations, building, testing, and iOS development.

🔍 Use 'production_readiness_dashboard' tool for system health
📊 Use 'performance_dashboard' tool for performance metrics
`);
  }

  /**
   * Format uptime duration
   */
  private formatUptime(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  /**
   * Main initialization and startup
   */
  async start(): Promise<void> {
    this.startTime = Date.now();

    try {
      this.displayBanner();

      // Step 1: Initialize server
      this.displayInitProgress("Initializing server...", 1, 6);
      this.server = new XcodeServer();

      // Step 2: Initialize services
      this.displayInitProgress("Setting up dependency injection...", 2, 6);
      await new Promise((resolve) => setTimeout(resolve, 100)); // Brief pause for visual effect

      // Step 3: Register tools
      this.displayInitProgress("Registering 60+ MCP tools...", 3, 6);
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Step 4: Initialize caching
      this.displayInitProgress("Initializing advanced caching system...", 4, 6);
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Step 5: Detect project
      this.displayInitProgress("Detecting active Xcode project...", 5, 6);
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Step 6: Start server
      this.displayInitProgress("Starting MCP server...", 6, 6);
      await this.server.start();

      // Display final status
      this.displayServerStatus(this.server);
    } catch (error) {
      console.error(`
❌ STARTUP FAILED:
─────────────────────────────────────────────────────────────
${error instanceof Error ? error.message : String(error)}

🔧 TROUBLESHOOTING:
─────────────────────────────────────────────────────────────
1. Ensure Xcode is installed and accessible
2. Check that you have proper permissions
3. Verify your project directory is accessible
4. Try running with DEBUG=true for more details

For support, visit: https://github.com/r-huijts/xcode-mcp-server
`);
      process.exit(1);
    }
  }

  /**
   * Cleanup on shutdown
   */
  async shutdown(): Promise<void> {
    if (this.server) {
      console.error("\n🛑 Shutting down Xcode MCP Server...");
      this.server.dispose();
      console.error("✅ Server shutdown complete");
    }
  }
}

// Main function to initialize and start the server with enhanced CLI
async function main() {
  const cli = new XcodeMCPCLI();

  // Handle graceful shutdown
  process.on("SIGINT", async () => {
    await cli.shutdown();
    process.exit(0);
  });

  process.on("SIGTERM", async () => {
    await cli.shutdown();
    process.exit(0);
  });

  await cli.start();
}

// Start the server
main().catch((error) => {
  console.error(
    "Unhandled exception:",
    error instanceof Error ? error.message : String(error)
  );
  process.exit(1);
});
