#!/usr/bin/env node

import { XcodeServer } from "./server.js";
import * as os from "os";
import * as path from "path";

/**
 * Enhanced CLI interface with professional status reporting
 */
class XcodeMCPCLI {
  private server: XcodeServer | null = null;
  private startTime: number = 0;

  /**
   * Display startup banner
   */
  private displayBanner(): void {
    console.error(`🚀 Xcode MCP Server v1.0.3 - Starting...`);
  }

  /**
   * Display initialization progress
   */
  private displayInitProgress(step: string): void {
    console.error(`⚡ ${step}`);
  }

  /**
   * Display server status
   */
  private displayServerStatus(server: XcodeServer): void {
    const activeProject = server.activeProject;

    console.error(`✅ Server ready - MCP protocol active`);

    if (activeProject) {
      console.error(`📁 Active: ${activeProject.name}`);
    } else {
      console.error(`⚠️  No active project - use set_project_path tool`);
    }
  }

  /**
   * Format uptime duration
   */
  private formatUptime(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  /**
   * Main initialization and startup
   */
  async start(): Promise<void> {
    this.startTime = Date.now();

    try {
      this.displayBanner();

      this.displayInitProgress("Initializing server...");
      this.server = new XcodeServer();

      this.displayInitProgress("Starting MCP server...");
      await this.server.start();

      // Display final status
      this.displayServerStatus(this.server);
    } catch (error) {
      console.error(`
❌ STARTUP FAILED:
─────────────────────────────────────────────────────────────
${error instanceof Error ? error.message : String(error)}

🔧 TROUBLESHOOTING:
─────────────────────────────────────────────────────────────
1. Ensure Xcode is installed and accessible
2. Check that you have proper permissions
3. Verify your project directory is accessible
4. Try running with DEBUG=true for more details

For support, visit: https://github.com/r-huijts/xcode-mcp-server
`);
      process.exit(1);
    }
  }

  /**
   * Cleanup on shutdown
   */
  async shutdown(): Promise<void> {
    if (this.server) {
      console.error("\n🛑 Shutting down Xcode MCP Server...");
      this.server.dispose();
      console.error("✅ Server shutdown complete");
    }
  }
}

// Main function to initialize and start the server with enhanced CLI
async function main() {
  const cli = new XcodeMCPCLI();

  // Handle graceful shutdown
  process.on("SIGINT", async () => {
    await cli.shutdown();
    process.exit(0);
  });

  process.on("SIGTERM", async () => {
    await cli.shutdown();
    process.exit(0);
  });

  await cli.start();
}

// Start the server
main().catch((error) => {
  console.error(
    "Unhandled exception:",
    error instanceof Error ? error.message : String(error)
  );
  process.exit(1);
});
